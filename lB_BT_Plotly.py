"""
LongBridge + Backtrader MACD回测系统
=====================================

这是一个完整的量化交易回测系统，具有以下功能：
1. 使用LongPort OpenAPI获取实时历史股票数据
2. 基于MACD指标实现量化交易策略
3. 使用Backtrader框架进行专业回测
4. 使用Plotly生成交互式可视化图表
5. 提供详细的交易统计和风险分析

主要组件：
- LongBridgeData: 数据下载器，负责从LongPort API获取历史K线数据
- MACDStrategy: MACD交易策略实现
- BacktestSystem: 回测系统主类，整合所有功能

作者: AI Assistant
版本: 1.0
"""

# 导入必要的库
import pandas as pd              # 数据处理和分析
import numpy as np               # 数值计算
import backtrader as bt          # 回测框架
import plotly.graph_objects as go # Plotly图表对象
import plotly.express as px      # Plotly快速绘图
from plotly.subplots import make_subplots  # 创建子图
from datetime import datetime, timedelta, date  # 日期时间处理
import os                        # 操作系统接口
from longport.openapi import QuoteContext, Config, Period, AdjustType  # LongPort API
import time                      # 时间相关功能
import warnings                  # 警告控制
import pickle                    # 数据序列化
import hashlib                   # 哈希计算
import json                      # JSON处理
warnings.filterwarnings('ignore')  # 忽略警告信息，保持输出清洁


class BacktestPlotter:
    """
    回测结果绘图器
    ==============

    专门用于绘制回测结果的可视化图表类。
    提供灵活的绘图功能，支持多种图表类型和自定义配置。

    主要功能：
    1. 绘制价格走势图（K线图）
    2. 绘制技术指标图（MACD等）
    3. 标记交易信号点
    4. 处理非交易日显示问题
    5. 生成交互式图表

    设计特点：
    - 面向对象设计，易于扩展
    - 支持多种技术指标
    - 自动处理时间轴连续性
    - 可自定义图表样式
    - 支持伪装模式，隐藏金融术语
    """

    def __init__(self, figsize=(1600, 1000), theme='plotly_white', fullscreen=True, disguise_mode=False):
        """
        初始化绘图器

        Args:
            figsize (tuple): 图表尺寸 (宽度, 高度)
            theme (str): 图表主题
            fullscreen (bool): 是否全屏显示
            disguise_mode (bool): 是否启用伪装模式，将金融术语替换为频率术语
        """
        self.figsize = figsize
        self.theme = theme
        self.fullscreen = fullscreen
        self.disguise_mode = disguise_mode
        self.colors = {
            'buy_signal': 'black',
            'sell_signal': 'red',
            'macd_line': 'blue',
            'signal_line': 'orange',
            'histogram_positive': 'green',
            'histogram_negative': 'red'
        }

        # 伪装术语映射表
        self.disguise_terms = {
            # 基本术语
            '价格': '频率',
            '收盘': '采样',
            '开盘': '初始',
            '最高': '峰值',
            '最低': '谷值',
            '成交量': '数据量',
            '买入': '增强',
            '卖出': '衰减',
            '信号': '响应',
            '策略': '滤波器',
            '回测': '频响分析',
            '交易': '调制',

            # MACD相关
            'MACD': '频差',
            'Signal': '基准',
            '直方图': '差分谱',
            '金叉': '正交叉',
            '死叉': '负交叉',

            # 统计术语
            '总收益率': '总增益',
            '夏普比率': '信噪比',
            '最大回撤': '最大衰减',
            '胜率': '有效率',
            '盈利': '增益',
            '亏损': '损耗',
            '利润': '净增益',

            # 单位和符号
            '$': 'Hz',
            '%': 'dB',
            '日期': '时间点',
            '交易日': '采样点'
        }

    def _apply_disguise(self, text):
        """
        应用伪装术语替换

        Args:
            text (str): 原始文本

        Returns:
            str: 替换后的文本
        """
        if not self.disguise_mode:
            return text

        result = text
        for original, disguised in self.disguise_terms.items():
            result = result.replace(original, disguised)
        return result

    def _prepare_data_for_plotting(self, df):
        """
        准备绘图数据，处理非交易日问题

        通过重新索引数据，确保图表中只显示交易日，避免非交易日的空白。

        Args:
            df (pd.DataFrame): 原始数据

        Returns:
            pd.DataFrame: 处理后的数据，带有连续的整数索引
        """
        # 创建数据副本，避免修改原始数据
        plot_df = df.copy()

        # 保存原始日期索引用于显示
        plot_df['original_date'] = plot_df.index

        # 创建连续的整数索引，消除非交易日间隙
        plot_df.reset_index(drop=True, inplace=True)

        return plot_df

    def _create_custom_tickvals_and_labels(self, plot_df, max_ticks=10):
        """
        创建自定义的x轴刻度值和标签

        Args:
            plot_df (pd.DataFrame): 绘图数据
            max_ticks (int): 最大刻度数量

        Returns:
            tuple: (刻度位置列表, 刻度标签列表)
        """
        total_points = len(plot_df)

        # 计算刻度间隔
        if total_points <= max_ticks:
            step = 1
        else:
            step = total_points // max_ticks

        # 生成刻度位置（整数索引）
        tickvals = list(range(0, total_points, step))
        if tickvals[-1] != total_points - 1:
            tickvals.append(total_points - 1)

        # 生成刻度标签（日期字符串）
        ticktext = []
        for idx in tickvals:
            if idx < len(plot_df):
                date_str = plot_df.iloc[idx]['original_date'].strftime('%Y-%m-%d')
                ticktext.append(date_str)
            else:
                ticktext.append('')

        return tickvals, ticktext

    def plot_macd_strategy_results(self, results_dict):
        """
        绘制MACD策略回测结果

        Args:
            results_dict (dict): 回测结果字典

        Returns:
            plotly.graph_objects.Figure: 绘制完成的图表对象
        """
        symbol = results_dict['symbol']
        df = results_dict['data'].copy()

        # 准备绘图数据
        plot_df = self._prepare_data_for_plotting(df)

        # 计算MACD指标
        plot_df = self._calculate_macd_indicators(plot_df)

        # 识别交易信号
        buy_signals, sell_signals = self._identify_trading_signals(plot_df)

        # 创建子图布局
        fig = self._create_subplot_layout(symbol)

        # 添加价格图表
        self._add_price_chart(fig, plot_df, buy_signals, sell_signals)

        # 添加MACD指标图
        self._add_macd_chart(fig, plot_df)

        # # 添加MACD直方图
        self._add_macd_histogram(fig, plot_df)

        # 添加修改后的直方图指标图
        self._add_modified_histogram(fig, plot_df)

        # 更新布局和样式
        self._update_layout(fig, plot_df, results_dict)

        return fig

    def _calculate_macd_indicators(self, plot_df):
        """
        计算MACD指标

        Args:
            plot_df (pd.DataFrame): 绘图数据

        Returns:
            pd.DataFrame: 添加了MACD指标的数据
        """
        # 计算MACD指标
        exp1 = plot_df['close'].ewm(span=12).mean()    # 12日指数移动平均
        exp2 = plot_df['close'].ewm(span=26).mean()    # 26日指数移动平均
        plot_df['macd'] = exp1 - exp2                  # MACD线
        plot_df['signal'] = plot_df['macd'].ewm(span=9).mean()  # 信号线
        plot_df['histogram'] = plot_df['macd'] - plot_df['signal']  # 直方图

        return plot_df

    def _identify_trading_signals(self, plot_df):
        """
        # todo 这个是需要根据自己的策略来修改的
        识别交易信号

        Args:
            plot_df (pd.DataFrame): 包含MACD指标的数据

        Returns:
            tuple: (买入信号列表, 卖出信号列表)
        """
        buy_signals = []
        sell_signals = []

        # # 遍历数据识别交叉信号
        # for i in range(1, len(plot_df)):
        #     # 买入信号：MACD金叉
        #     if (plot_df['macd'].iloc[i] > plot_df['signal'].iloc[i] and
        #         plot_df['macd'].iloc[i-1] <= plot_df['signal'].iloc[i-1]):
        #         buy_signals.append((i, plot_df['close'].iloc[i]))
        #
        #     # 卖出信号：MACD死叉
        #     elif (plot_df['macd'].iloc[i] < plot_df['signal'].iloc[i] and
        #           plot_df['macd'].iloc[i-1] >= plot_df['signal'].iloc[i-1]):
        #         sell_signals.append((i, plot_df['close'].iloc[i]))
        # 遍历数据识别交叉信号
        histogram_list = list(plot_df['histogram'])
        histogram_diff = np.diff(histogram_list)
        histogram_diff = np.insert(histogram_diff, 0, 0)
        for i in range(1, len(plot_df)):
            # 买入信号：MACD金叉
            if (histogram_diff[i]*histogram_diff[i-1] < 0 and histogram_diff[i] > 0): # 当前值大于0，表示升
                buy_signals.append((i, plot_df['close'].iloc[i]))

            # 卖出信号：MACD死叉
            elif (histogram_diff[i]*histogram_diff[i-1] < 0 and histogram_diff[i] < 0): # 当前值大于0，表示降
                sell_signals.append((i, plot_df['close'].iloc[i]))

        return buy_signals, sell_signals

    def _create_subplot_layout(self, symbol):
        """
        创建子图布局

        Args:
            symbol (str): 股票代码

        Returns:
            plotly.graph_objects.Figure: 子图布局
        """
        # 应用伪装术语
        if self.disguise_mode:
            title1 = f'{symbol} 频率响应与调制信号'
            title2 = '频差指标'
            title3 = '频差差分谱'
        else:
            title1 = f'{symbol} 价格走势与交易信号'
            title2 = 'MACD指标'
            title3 = 'MACD柱状图'

        fig = make_subplots(
            rows=4, cols=1,
            shared_xaxes=True,
            vertical_spacing=0.03,
            row_heights=[0.5, 0.1, 0.2, 0.2],  # 调整子图高度比例
            subplot_titles=(title1, title2, title3)
        )
        return fig

    def _add_price_chart(self, fig, plot_df, buy_signals, sell_signals):
        """
        添加价格图表

        Args:
            fig: Plotly图表对象
            plot_df: 绘图数据
            buy_signals: 买入信号列表
            sell_signals: 卖出信号列表
        """
        # 创建自定义hover文本，显示日期而不是索引
        hover_text = []
        for i in range(len(plot_df)):
            date_str = plot_df.iloc[i]['original_date'].strftime('%Y-%m-%d')
            if self.disguise_mode:
                hover_text.append(
                    f"时间点: {date_str}<br>" +
                    f"初始: {plot_df['open'].iloc[i]:.2f}Hz<br>" +
                    f"峰值: {plot_df['high'].iloc[i]:.2f}Hz<br>" +
                    f"谷值: {plot_df['low'].iloc[i]:.2f}Hz<br>" +
                    f"采样: {plot_df['close'].iloc[i]:.2f}Hz<br>" +
                    f"数据量: {plot_df['volume'].iloc[i]:,}"
                )
            else:
                hover_text.append(
                    f"日期: {date_str}<br>" +
                    f"开盘: ${plot_df['open'].iloc[i]:.2f}<br>" +
                    f"最高: ${plot_df['high'].iloc[i]:.2f}<br>" +
                    f"最低: ${plot_df['low'].iloc[i]:.2f}<br>" +
                    f"收盘: ${plot_df['close'].iloc[i]:.2f}<br>" +
                    f"成交量: {plot_df['volume'].iloc[i]:,}"
                )

        # 添加K线图
        fig.add_trace(
            go.Candlestick(
                x=list(range(len(plot_df))),  # 使用连续整数作为x轴
                open=plot_df['open'],
                high=plot_df['high'],
                low=plot_df['low'],
                close=plot_df['close'],
                name='价格',
                showlegend=False,
                hovertext=hover_text,  # 自定义hover文本
                hoverinfo='text'       # 只显示自定义文本
            ),
            row=1, col=1
        )

        # 添加买入信号
        if buy_signals:
            buy_x, buy_y = zip(*buy_signals)
            # 创建买入信号的hover文本
            buy_hover_text = []
            for i, (x_idx, price) in enumerate(buy_signals):
                date_str = plot_df.iloc[x_idx]['original_date'].strftime('%Y-%m-%d')
                if self.disguise_mode:
                    buy_hover_text.append(f"增强响应<br>时间点: {date_str}<br>频率: {price:.2f}Hz")
                    signal_name = '增强响应'
                else:
                    buy_hover_text.append(f"买入信号<br>日期: {date_str}<br>价格: ${price:.2f}")
                    signal_name = '买入信号'

            fig.add_trace(
                go.Scatter(
                    x=list(buy_x),
                    y=list(buy_y),
                    mode='markers',
                    marker=dict(
                        symbol='triangle-up',
                        size=12,
                        color=self.colors['buy_signal'],
                        line=dict(width=2, color='darkgreen')
                    ),
                    name=signal_name,
                    showlegend=True,
                    hovertext=buy_hover_text,
                    hoverinfo='text'
                ),
                row=1, col=1
            )

        # 添加卖出信号
        if sell_signals:
            sell_x, sell_y = zip(*sell_signals)
            # 创建卖出信号的hover文本
            sell_hover_text = []
            for i, (x_idx, price) in enumerate(sell_signals):
                date_str = plot_df.iloc[x_idx]['original_date'].strftime('%Y-%m-%d')
                if self.disguise_mode:
                    sell_hover_text.append(f"衰减响应<br>时间点: {date_str}<br>频率: {price:.2f}Hz")
                    signal_name = '衰减响应'
                else:
                    sell_hover_text.append(f"卖出信号<br>日期: {date_str}<br>价格: ${price:.2f}")
                    signal_name = '卖出信号'

            fig.add_trace(
                go.Scatter(
                    x=list(sell_x),
                    y=list(sell_y),
                    mode='markers',
                    marker=dict(
                        symbol='triangle-down',
                        size=12,
                        color=self.colors['sell_signal'],
                        line=dict(width=2, color='darkred')
                    ),
                    name=signal_name,
                    showlegend=True,
                    hovertext=sell_hover_text,
                    hoverinfo='text'
                ),
                row=1, col=1
            )

    def _add_macd_chart(self, fig, plot_df):
        """
        添加MACD指标图

        Args:
            fig: Plotly图表对象
            plot_df: 绘图数据
        """
        x_values = list(range(len(plot_df)))

        # 创建MACD线的hover文本
        macd_hover_text = []
        for i in range(len(plot_df)):
            date_str = plot_df.iloc[i]['original_date'].strftime('%Y-%m-%d')
            if self.disguise_mode:
                macd_hover_text.append(
                    f"时间点: {date_str}<br>频差: {plot_df['macd'].iloc[i]:.4f}"
                )
                macd_name = '频差'
            else:
                macd_hover_text.append(
                    f"日期: {date_str}<br>MACD: {plot_df['macd'].iloc[i]:.4f}"
                )
                macd_name = 'MACD'

        # 创建信号线的hover文本
        signal_hover_text = []
        for i in range(len(plot_df)):
            date_str = plot_df.iloc[i]['original_date'].strftime('%Y-%m-%d')
            if self.disguise_mode:
                signal_hover_text.append(
                    f"时间点: {date_str}<br>基准: {plot_df['signal'].iloc[i]:.4f}"
                )
                signal_name = '基准'
            else:
                signal_hover_text.append(
                    f"日期: {date_str}<br>Signal: {plot_df['signal'].iloc[i]:.4f}"
                )
                signal_name = 'Signal'

        # 添加MACD线
        fig.add_trace(
            go.Scatter(
                x=x_values,
                y=plot_df['macd'],
                line=dict(color=self.colors['macd_line'], width=2),
                name=macd_name,
                showlegend=True,
                hovertext=macd_hover_text,
                hoverinfo='text'
            ),
            row=2, col=1
        )

        # 添加信号线
        fig.add_trace(
            go.Scatter(
                x=x_values,
                y=plot_df['signal'],
                line=dict(color=self.colors['signal_line'], width=2),
                name=signal_name,
                showlegend=True,
                hovertext=signal_hover_text,
                hoverinfo='text'
            ),
            row=2, col=1
        )

        # 添加零轴线
        fig.add_hline(y=0, line_dash="dash", line_color="gray",
                     opacity=0.5, row=2, col=1)
    def _add_modified_histogram(self, fig, plot_df):
        """
        添加MACD直方图

        Args:
            fig: Plotly图表对象
            plot_df: 绘图数据
        """
        x_values = list(range(len(plot_df)))
        colors = [self.colors['histogram_positive'] if val >= 0
                 else self.colors['histogram_negative']
                 for val in plot_df['histogram']]

        # 创建直方图的hover文本
        histogram_hover_text = []
        for i in range(len(plot_df)):
            date_str = plot_df.iloc[i]['original_date'].strftime('%Y-%m-%d')
            if self.disguise_mode:
                histogram_hover_text.append(
                    f"时间点: {date_str}<br>差分谱: {plot_df['histogram'].iloc[i]:.4f}"
                )
                histogram_name = '修正差分谱'
            else:
                histogram_hover_text.append(
                    f"日期: {date_str}<br>直方图: {plot_df['histogram'].iloc[i]:.4f}"
                )
                histogram_name = 'modified Histogram'

        fig.add_trace(
            go.Bar(
                x=x_values,
                y=plot_df['histogram'],
                marker_color=colors,
                name=histogram_name,
                showlegend=True,
                opacity=0.7,
                hovertext=histogram_hover_text,
                hoverinfo='text'
            ),
            row=4, col=1
        )

        # 添加零轴线
        fig.add_hline(y=0, line_dash="dash", line_color="gray",
                     opacity=0.5, row=3, col=1)

    def _add_macd_histogram(self, fig, plot_df):
        """
        添加MACD直方图

        Args:
            fig: Plotly图表对象
            plot_df: 绘图数据
        """
        x_values = list(range(len(plot_df)))
        colors = [self.colors['histogram_positive'] if val >= 0
                 else self.colors['histogram_negative']
                 for val in plot_df['histogram']]

        # 创建直方图的hover文本
        histogram_hover_text = []
        for i in range(len(plot_df)):
            date_str = plot_df.iloc[i]['original_date'].strftime('%Y-%m-%d')
            if self.disguise_mode:
                histogram_hover_text.append(
                    f"时间点: {date_str}<br>差分谱: {plot_df['histogram'].iloc[i]:.4f}"
                )
                histogram_name = '频差差分谱'
            else:
                histogram_hover_text.append(
                    f"日期: {date_str}<br>直方图: {plot_df['histogram'].iloc[i]:.4f}"
                )
                histogram_name = 'MACD Histogram'

        fig.add_trace(
            go.Bar(
                x=x_values,
                y=plot_df['histogram'],
                marker_color=colors,
                name=histogram_name,
                showlegend=True,
                opacity=0.7,
                hovertext=histogram_hover_text,
                hoverinfo='text'
            ),
            row=3, col=1
        )

        # 添加零轴线
        fig.add_hline(y=0, line_dash="dash", line_color="gray",
                     opacity=0.5, row=3, col=1)

    def _update_layout(self, fig, plot_df, results_dict):
        """
        更新图表布局和样式

        Args:
            fig: Plotly图表对象
            plot_df: 绘图数据
            results_dict: 回测结果字典
        """
        # 创建自定义x轴刻度
        tickvals, ticktext = self._create_custom_tickvals_and_labels(plot_df)

        # 处理夏普比率显示
        sharpe_text = (f'{results_dict["sharpe_ratio"]:.4f}'
                      if results_dict["sharpe_ratio"] is not None else 'N/A')

        # 根据伪装模式设置标题
        if self.disguise_mode:
            title_text = f'{results_dict["symbol"]} 频差滤波器频响分析结果<br>' + \
                        f'<sub>总增益: {results_dict["total_return"]:.2f}dB | ' + \
                        f'信噪比: {sharpe_text} | ' + \
                        f'最大衰减: {results_dict["max_drawdown"]:.2f}dB | ' + \
                        f'有效率: {results_dict["win_rate"]:.1f}%</sub>'
        else:
            title_text = f'{results_dict["symbol"]} MACD策略回测结果<br>' + \
                        f'<sub>总收益率: {results_dict["total_return"]:.2f}% | ' + \
                        f'夏普比率: {sharpe_text} | ' + \
                        f'最大回撤: {results_dict["max_drawdown"]:.2f}% | ' + \
                        f'胜率: {results_dict["win_rate"]:.1f}%</sub>'

        # 更新布局
        layout_config = {
            'title': {
                'text': title_text,
                'x': 0.5,
                'xanchor': 'center',
                'font': {'size': 16}
            },
            'showlegend': True,
            'legend': dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1
            ),
            'template': self.theme,
            'margin': dict(l=50, r=50, t=100, b=50)  # 减少边距以最大化图表区域
        }

        # 根据是否全屏设置不同的尺寸
        if self.fullscreen:
            layout_config.update({
                'width': None,   # 让浏览器自动调整宽度
                'height': None,  # 让浏览器自动调整高度
                'autosize': True  # 自动调整大小以适应容器
            })
        else:
            layout_config.update({
                'width': self.figsize[0],
                'height': self.figsize[1]
            })

        fig.update_layout(**layout_config)

        # 更新所有x轴使用自定义刻度
        for i in range(1, 4):
            fig.update_xaxes(
                tickvals=tickvals,
                ticktext=ticktext,
                tickangle=45,
                row=i, col=1
            )

        # 更新y轴标签
        if self.disguise_mode:
            fig.update_xaxes(title_text="采样点", row=3, col=1)
            fig.update_yaxes(title_text="频率 (Hz)", row=1, col=1)
            fig.update_yaxes(title_text="频差值", row=2, col=1)
            fig.update_yaxes(title_text="差分谱值", row=3, col=1)
            fig.update_yaxes(title_text="滤波趋势", row=4, col=1)
        else:
            fig.update_xaxes(title_text="交易日期", row=3, col=1)
            fig.update_yaxes(title_text="价格 ($)", row=1, col=1)
            fig.update_yaxes(title_text="MACD值", row=2, col=1)
            fig.update_yaxes(title_text="直方图值", row=3, col=1)
            fig.update_yaxes(title_text="滤波趋势", row=4, col=1)

        # 移除x轴范围滑块
        fig.update_layout(xaxis_rangeslider_visible=False)


class DataCacheManager:
    """
    数据缓存管理器（CSV格式）
    ========================

    负责管理LongBridge数据的本地缓存，提供数据的保存、读取和管理功能。

    功能特点：
    1. 自动创建缓存目录结构
    2. 基于股票代码和日期范围生成唯一缓存键
    3. 支持CSV格式数据存储和读取
    4. 提供缓存有效性检查
    5. 支持缓存清理和管理

    缓存策略：
    - 使用CSV格式存储DataFrame数据，便于查看和编辑
    - 缓存文件命名：{symbol}_{start_date}_{end_date}.csv
    - 支持元数据存储，记录缓存创建时间等信息
    - CSV文件包含完整的OHLCV数据和时间索引
    """

    def __init__(self, cache_dir="data_cache"):
        """
        初始化缓存管理器

        Args:
            cache_dir (str): 缓存目录路径，默认为"data_cache"
        """
        self.cache_dir = cache_dir
        self.metadata_file = os.path.join(cache_dir, "cache_metadata.json")
        self._ensure_cache_directory()
        self._load_metadata()

    def _ensure_cache_directory(self):
        """确保缓存目录存在"""
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir)
            print(f"创建缓存目录: {self.cache_dir}")

    def _load_metadata(self):
        """加载缓存元数据"""
        if os.path.exists(self.metadata_file):
            try:
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    self.metadata = json.load(f)
            except Exception as e:
                print(f"加载缓存元数据失败: {e}")
                self.metadata = {}
        else:
            self.metadata = {}

    def _save_metadata(self):
        """保存缓存元数据"""
        try:
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, ensure_ascii=False, indent=2, default=str)
        except Exception as e:
            print(f"保存缓存元数据失败: {e}")

    def _generate_cache_key(self, symbol, start_date, end_date, period="Day"):
        """
        生成缓存键（CSV文件名）

        Args:
            symbol (str): 股票代码
            start_date (datetime): 开始日期
            end_date (datetime): 结束日期
            period (str): 时间周期，如"Day", "Min_1", "Min_5"等

        Returns:
            str: 缓存键（不含扩展名）
        """
        # 将日期转换为字符串
        start_str = start_date.strftime('%Y%m%d') if isinstance(start_date, datetime) else start_date.strftime('%Y%m%d')
        end_str = end_date.strftime('%Y%m%d') if isinstance(end_date, datetime) else end_date.strftime('%Y%m%d')

        # 创建包含时间周期的文件名：股票代码_周期_开始日期_结束日期
        # 例如：AAPL.US_Day_20230101_20231231
        #      AAPL.US_Min_5_20230101_20231231
        return f"{symbol}_{period}_{start_str}_{end_str}"

    def _get_cache_file_path(self, cache_key):
        """获取缓存文件路径（CSV格式）"""
        return os.path.join(self.cache_dir, f"{cache_key}.csv")

    def has_cached_data(self, symbol, start_date, end_date, period="Day"):
        """
        检查是否存在缓存数据

        Args:
            symbol (str): 股票代码
            start_date (datetime): 开始日期
            end_date (datetime): 结束日期
            period (str): 时间周期，如"Day", "Min_1", "Min_5"等

        Returns:
            bool: 是否存在有效缓存
        """
        cache_key = self._generate_cache_key(symbol, start_date, end_date, period)
        cache_file = self._get_cache_file_path(cache_key)

        return os.path.exists(cache_file) and cache_key in self.metadata

    def save_data(self, symbol, start_date, end_date, data, period="Day"):
        """
        保存数据到缓存（CSV格式）

        Args:
            symbol (str): 股票代码
            start_date (datetime): 开始日期
            end_date (datetime): 结束日期
            data (pd.DataFrame): 要缓存的数据
            period (str): 时间周期，如"Day", "Min_1", "Min_5"等

        Returns:
            bool: 保存是否成功
        """
        try:
            cache_key = self._generate_cache_key(symbol, start_date, end_date, period)
            cache_file = self._get_cache_file_path(cache_key)

            # 保存数据为CSV格式
            # 确保索引（日期时间）也被保存，并命名为datetime
            data_to_save = data.copy()
            data_to_save.reset_index(inplace=True)  # 将datetime索引转为列

            # 确保索引列名为datetime
            if data_to_save.columns[0] != 'datetime':
                data_to_save.rename(columns={data_to_save.columns[0]: 'datetime'}, inplace=True)

            data_to_save.to_csv(cache_file, index=False, encoding='utf-8')

            # 更新元数据
            self.metadata[cache_key] = {
                'symbol': symbol,
                'period': period,
                'start_date': start_date.isoformat() if isinstance(start_date, datetime) else start_date.isoformat(),
                'end_date': end_date.isoformat() if isinstance(end_date, datetime) else end_date.isoformat(),
                'cached_at': datetime.now().isoformat(),
                'data_points': len(data),
                'file_size': os.path.getsize(cache_file),
                'format': 'csv'
            }

            self._save_metadata()

            print(f"数据已缓存到CSV: {cache_file} ({len(data)} 条记录)")
            return True

        except Exception as e:
            print(f"缓存数据失败: {e}")
            return False

    def load_data(self, symbol, start_date, end_date, period="Day"):
        """
        从缓存加载数据（CSV格式）

        Args:
            symbol (str): 股票代码
            start_date (datetime): 开始日期
            end_date (datetime): 结束日期
            period (str): 时间周期，如"Day", "Min_1", "Min_5"等

        Returns:
            pd.DataFrame or None: 缓存的数据，如果不存在则返回None
        """
        try:
            cache_key = self._generate_cache_key(symbol, start_date, end_date, period)
            cache_file = self._get_cache_file_path(cache_key)

            if not os.path.exists(cache_file):
                return None

            # 从CSV加载数据
            data = pd.read_csv(cache_file, encoding='utf-8')

            # 将datetime列转换回索引
            if 'datetime' in data.columns:
                data['datetime'] = pd.to_datetime(data['datetime'])
                data.set_index('datetime', inplace=True)

            print(f"从CSV缓存加载数据: {cache_file} ({len(data)} 条记录)")
            return data

        except Exception as e:
            print(f"加载CSV缓存数据失败: {e}")
            return None

    def get_cache_info(self):
        """
        获取缓存信息

        Returns:
            dict: 缓存统计信息
        """
        total_files = len(self.metadata)
        total_size = sum(item.get('file_size', 0) for item in self.metadata.values())

        return {
            'total_cached_files': total_files,
            'total_cache_size_bytes': total_size,
            'total_cache_size_mb': total_size / (1024 * 1024),
            'cache_directory': self.cache_dir
        }

    def clear_cache(self, symbol=None):
        """
        清理缓存

        Args:
            symbol (str, optional): 如果指定，只清理该股票的缓存；否则清理全部
        """
        try:
            cleared_count = 0

            if symbol:
                # 清理特定股票的缓存
                keys_to_remove = []
                for cache_key, metadata in self.metadata.items():
                    if metadata.get('symbol') == symbol:
                        cache_file = self._get_cache_file_path(cache_key)
                        if os.path.exists(cache_file):
                            os.remove(cache_file)
                        keys_to_remove.append(cache_key)
                        cleared_count += 1

                for key in keys_to_remove:
                    del self.metadata[key]

                print(f"已清理 {symbol} 的 {cleared_count} 个缓存文件")
            else:
                # 清理全部缓存
                for cache_key in list(self.metadata.keys()):
                    cache_file = self._get_cache_file_path(cache_key)
                    if os.path.exists(cache_file):
                        os.remove(cache_file)
                    cleared_count += 1

                self.metadata.clear()
                print(f"已清理全部 {cleared_count} 个缓存文件")

            self._save_metadata()

        except Exception as e:
            print(f"清理缓存失败: {e}")


class LongBridgeData:
    """
    LongBridge数据下载器（带缓存功能）
    ===============================

    这个类负责从LongPort OpenAPI获取历史股票数据，并提供本地缓存功能。
    LongPort是一个专业的港美股交易平台，提供丰富的市场数据API。

    功能特点：
    - 支持港股、美股、A股等多个市场
    - 提供实时和历史K线数据
    - 支持多种复权方式
    - 数据质量高，延迟低
    - **新增：本地数据缓存功能**
    - **新增：优先使用离线数据，减少API调用**

    缓存策略：
    1. 首次下载数据时自动保存到本地缓存
    2. 后续请求相同数据时优先从缓存读取
    3. 支持缓存管理和清理功能
    4. 缓存失效时自动重新下载

    使用前需要：
    1. 在LongPort开发者中心申请API权限
    2. 设置环境变量：LONGPORT_APP_KEY, LONGPORT_APP_SECRET, LONGPORT_ACCESS_TOKEN
    3. 确保有相应市场的行情权限
    """

    def __init__(self, enable_cache=True, cache_dir="data_cache"):
        """
        初始化LongBridge连接和缓存管理器

        从环境变量中读取API配置信息并创建行情上下文。
        同时初始化数据缓存管理器。

        Args:
            enable_cache (bool): 是否启用缓存功能，默认True
            cache_dir (str): 缓存目录路径，默认"data_cache"

        需要预先设置以下环境变量：
        - LONGPORT_APP_KEY: 应用密钥
        - LONGPORT_APP_SECRET: 应用秘密
        - LONGPORT_ACCESS_TOKEN: 访问令牌

        Raises:
            Exception: 如果环境变量未设置或API连接失败
        """
        # 从环境变量加载配置
        self.config = Config.from_env()
        # 创建行情数据上下文，用于获取市场数据
        self.ctx = QuoteContext(self.config)

        # 初始化缓存功能
        self.enable_cache = enable_cache
        if self.enable_cache:
            self.cache_manager = DataCacheManager(cache_dir)
            print(f"缓存功能已启用，缓存目录: {cache_dir}")
        else:
            self.cache_manager = None
            print("缓存功能已禁用")

    def download_data(self, symbol, start_date, end_date, period=Period.Day, force_download=False):
        """
        下载历史K线数据（带缓存功能，支持多种时间周期）
        ===============================================

        优先从本地缓存获取数据，如果缓存不存在则从LongPort API获取并缓存。

        Args:
            symbol (str): 股票代码，格式为 'ticker.market'
                         例如：'AAPL.US' (苹果-美股)
                              '00700.HK' (腾讯-港股)
                              '000001.SZ' (平安银行-深股)
            start_date (datetime): 开始日期，支持datetime对象
            end_date (datetime): 结束日期，支持datetime对象
            period (Period): 时间周期，支持：
                           - Period.Day: 日线（默认）
                           - Period.Min_1: 1分钟线
                           - Period.Min_5: 5分钟线
                           - Period.Min_15: 15分钟线
                           - Period.Min_30: 30分钟线
                           - Period.Min_60: 60分钟线
                           - 其他LongPort支持的周期
            force_download (bool): 是否强制重新下载，忽略缓存，默认False

        Returns:
            pandas.DataFrame: 包含OHLCV数据的DataFrame，列包括：
                - datetime: 日期时间索引
                - open: 开盘价
                - high: 最高价
                - low: 最低价
                - close: 收盘价
                - volume: 成交量

        Raises:
            ValueError: 当无法获取数据时抛出异常

        Note:
            - 使用前复权数据，确保价格连续性
            - 支持多种时间周期，从1分钟到日线
            - 不同周期的数据分别缓存，互不干扰
            - 支持的历史数据范围因市场而异：
              * 美股：2010-06-01至今
              * 港股：2004-06-01至今
              * A股：1999-11-01至今
            - **缓存策略：优先使用本地缓存，减少API调用**
        """
        # 获取周期字符串用于显示和缓存
        period_str = str(period).split('.')[-1]  # 从Period.Day获取"Day"

        # 第一步：检查缓存（如果启用且不强制下载）
        if self.enable_cache and not force_download:
            print(f"检查 {symbol} ({period_str}) 从 {start_date.date()} 到 {end_date.date()} 的缓存数据...")

            cached_data = self.cache_manager.load_data(symbol, start_date, end_date, period_str)
            if cached_data is not None:
                print(f"✓ 使用缓存数据，共 {len(cached_data)} 条记录")
                return cached_data
            else:
                print("✗ 缓存中未找到数据，将从API下载")
        elif force_download:
            print(f"强制重新下载 {symbol} ({period_str}) 的数据...")
        else:
            print(f"缓存功能已禁用，直接从API下载 {symbol} ({period_str}) 的数据...")

        # 第二步：从API下载数据
        try:
            print(f"正在从LongPort API下载 {symbol} ({period_str}) 从 {start_date.date()} 到 {end_date.date()} 的数据...")

            # 转换datetime为date对象，因为API需要date类型参数
            start_date_obj = start_date.date() if isinstance(start_date, datetime) else start_date
            end_date_obj = end_date.date() if isinstance(end_date, datetime) else end_date

            # 调用LongPort API获取历史K线数据
            # 参数说明：
            # - symbol: 股票代码
            # - period: 时间周期（支持多种周期）
            # - AdjustType.ForwardAdjust: 前复权，处理分红送股影响
            # - start_date_obj: 开始日期
            # - end_date_obj: 结束日期
            resp = self.ctx.history_candlesticks_by_date(
                symbol,
                period,  # 使用传入的时间周期
                AdjustType.ForwardAdjust,  # 前复权
                start_date_obj,
                end_date_obj
            )

            # 检查API响应是否有效
            if not resp:
                raise ValueError(f"未能获取到 {symbol} 的数据")

            # 将API响应转换为pandas DataFrame
            # LongPort API返回的是Candlestick对象列表
            data = []
            for candle in resp:
                # 提取每根K线的OHLCV数据
                data.append({
                    'datetime': candle.timestamp,  # 时间戳（已经是datetime对象）
                    'open': float(candle.open),    # 开盘价（从Decimal转为float）
                    'high': float(candle.high),    # 最高价
                    'low': float(candle.low),      # 最低价
                    'close': float(candle.close),  # 收盘价
                    'volume': int(candle.volume)   # 成交量
                })

            # 创建DataFrame并设置时间索引
            df = pd.DataFrame(data)
            df.set_index('datetime', inplace=True)  # 将datetime设为索引
            df.sort_index(inplace=True)             # 按时间排序，确保数据顺序正确

            print(f"✓ 成功从API下载 {len(df)} 条数据")

            # 第三步：保存到缓存（如果启用缓存）
            if self.enable_cache:
                success = self.cache_manager.save_data(symbol, start_date, end_date, df, period_str)
                if success:
                    print(f"✓ 数据已保存到缓存")
                else:
                    print(f"✗ 数据缓存失败")

            return df

        except Exception as e:
            # 捕获并处理所有可能的异常
            print(f"✗ 数据下载失败: {e}")
            return None

    def get_cache_info(self):
        """
        获取缓存信息

        Returns:
            dict: 缓存统计信息，如果缓存未启用则返回None
        """
        if not self.enable_cache:
            print("缓存功能未启用")
            return None

        return self.cache_manager.get_cache_info()

    def clear_cache(self, symbol=None):
        """
        清理缓存

        Args:
            symbol (str, optional): 如果指定，只清理该股票的缓存；否则清理全部
        """
        if not self.enable_cache:
            print("缓存功能未启用")
            return

        self.cache_manager.clear_cache(symbol)

    def print_cache_info(self):
        """打印缓存信息"""
        if not self.enable_cache:
            print("缓存功能未启用")
            return

        cache_info = self.get_cache_info()
        if cache_info:
            print(f"\n{'='*30} 缓存信息 {'='*30}")
            print(f"缓存目录: {cache_info['cache_directory']}")
            print(f"缓存文件数量: {cache_info['total_cached_files']}")
            print(f"缓存总大小: {cache_info['total_cache_size_mb']:.2f} MB")
            print(f"{'='*70}")


class MACDStrategy(bt.Strategy):
    """
    MACD交易策略
    ============

    基于MACD（Moving Average Convergence Divergence）指标的量化交易策略。
    MACD是一个趋势跟踪动量指标，通过计算两个不同周期的指数移动平均线的差值来判断趋势。

    策略原理：
    1. MACD线 = 快速EMA - 慢速EMA
    2. 信号线 = MACD线的EMA
    3. 直方图 = MACD线 - 信号线

    交易信号：
    - 买入信号：MACD线从下方穿越信号线（金叉）
    - 卖出信号：MACD线从上方穿越信号线（死叉）

    策略特点：
    - 适用于趋势性市场
    - 滞后性指标，适合中长期交易
    - 在震荡市场中可能产生较多假信号

    参数说明：
    - fast_period: 快速EMA周期，默认12
    - slow_period: 慢速EMA周期，默认26
    - signal_period: 信号线EMA周期，默认9
    - printlog: 是否打印交易日志
    """

    # 策略参数定义

    params = (
        ('printlog', True),      # 是否打印交易日志
        ('fast_period', 12),  # 快线周期（短期EMA）
        ('slow_period', 26),  # 慢线周期（长期EMA）
        ('signal_period', 9),  # 信号线周期（MACD的EMA）
        ('origin_df', None),  # 原始数据DataFrame
    )
    
    def __init__(self):
        """
        策略初始化方法

        在这里定义所有需要的技术指标和交易信号。
        Backtrader会在策略开始前调用此方法进行初始化。
        """
        # 计算MACD指标（使用MACDHisto来获取完整的MACD指标，包括直方图）
        # MACDHisto包含三条线：macd线、signal线和histo直方图
        self.macd = bt.indicators.MACDHisto(
            self.data.close,                          # 使用收盘价计算
            period_me1=self.params.fast_period,      # 快速EMA周期
            period_me2=self.params.slow_period,      # 慢速EMA周期
            period_signal=self.params.signal_period  # 信号线EMA周期
        )

        # 提取MACD指标的各个组件，便于后续使用
        self.macd_line = self.macd.macd      # MACD主线（快EMA - 慢EMA）
        self.signal_line = self.macd.signal  # 信号线（MACD线的EMA）
        self.histogram = self.macd.histo     # 直方图（MACD线 - 信号线）
        self.histogram_list = []

        # 创建交叉信号指标
        # CrossOver指标用于检测两条线的交叉：
        # 返回值 > 0：MACD线从下方穿越信号线（金叉，买入信号）
        # 返回值 < 0：MACD线从上方穿越信号线（死叉，卖出信号）
        self.crossover = bt.indicators.CrossOver(self.macd_line, self.signal_line)

        # 初始化交易状态变量
        self.order = None    # 当前待执行订单，用于避免重复下单
        self.trades = []     # 交易记录列表，用于存储交易详情
        
    def notify_order(self, order):
        """
        订单状态通知回调方法

        当订单状态发生变化时，Backtrader会自动调用此方法。
        用于跟踪订单执行情况和记录交易日志。

        Args:
            order: 订单对象，包含订单的所有信息
        """
        # 检查订单是否已完成执行
        if order.status in [order.Completed]:
            # 根据订单类型记录不同的执行信息
            if order.isbuy():
                # 买入订单执行完成
                self.log(f'买入执行, 价格: {order.executed.price:.2f}, '
                        f'数量: {order.executed.size}, '
                        f'手续费: {order.executed.comm:.2f}')
            else:
                # 卖出订单执行完成
                self.log(f'卖出执行, 价格: {order.executed.price:.2f}, '
                        f'数量: {order.executed.size}, '
                        f'手续费: {order.executed.comm:.2f}')
        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            # 订单被取消、保证金不足或被拒绝
            self.log('订单取消/保证金不足/拒绝')

        # 清除订单引用，允许下新订单
        self.order = None
    
    def notify_trade(self, trade):
        """
        交易完成通知回调方法

        当一个完整的交易（买入+卖出）完成时，Backtrader会调用此方法。
        用于记录交易盈亏和统计信息。

        Args:
            trade: 交易对象，包含交易的盈亏信息
        """
        # 只处理已关闭的交易（即买入和卖出都已完成）
        if not trade.isclosed:
            return

        # 记录交易盈亏信息
        self.log(f'交易利润, 毛利润 {trade.pnl:.2f}, 净利润 {trade.pnlcomm:.2f}')

        # 将交易详情添加到交易记录列表
        self.trades.append({
            'date': self.data.datetime.date(0),  # 交易完成日期
            'pnl': trade.pnl,                    # 毛利润（不含手续费）
            'pnlcomm': trade.pnlcomm             # 净利润（含手续费）
        })

    def get_distogram_diff_signal(self, hg):
        histogram_diff = np.diff(hg)
        histogram_diff = np.insert(histogram_diff, 0, 0)
        if histogram_diff[-1]*histogram_diff[-2] <=0 and histogram_diff[-1] > 0:
            return 1
        elif histogram_diff[-1]*histogram_diff[-2] <=0 and histogram_diff[-1] < 0:
            return -1
        else:
            return 0
    
    def next(self):
        """
        策略主逻辑方法

        这是策略的核心方法，Backtrader会在每个数据点（每个交易日）调用此方法。
        在这里实现具体的交易逻辑和信号判断。

        MACD策略逻辑：
        1. 检查是否有未完成的订单，避免重复下单
        2. 当没有持仓且出现金叉信号时，执行买入
        3. 当有持仓且出现死叉信号时，执行卖出
        """
        # 如果有未执行的订单，等待其完成，避免重复下单
        if self.order:
            return

        # 在这里设置断点
        current_date = self.data.datetime.date(0)
        current_close = self.data.close[0]
        current_macd = self.macd_line[0]
        current_signal = self.signal_line[0]
        current_histogram = self.histogram[0]
        self.histogram_list.append(current_histogram)
        # 添加调试信息
        debug_info = {
            'date': current_date,
            'close': current_close,
            'macd': current_macd,
            'signal': current_signal,
            'histogram': current_histogram,
            'position': self.position,
            'crossover': self.crossover
        }
        
        # 在这里设置断点，查看 debug_info 的值
        
        if len(self.histogram_list) <3:
            return
    
        # 交易信号判断和执行
        signal = self.get_distogram_diff_signal(self.histogram_list)
                # 买入信号：没有持仓 且 MACD金叉（MACD线从下方穿越信号线）
        if  signal == 1:
            self.log(f'MACD金叉买入信号, 当前收盘价: {self.data.close[0]:.2f}')
            self.log(f'  -> 订单将在下一交易日执行')
            # 执行市价买入，买入全部可用资金
            self.order = self.buy(exectype=bt.Order.Close)  # 使用当日收盘价执行（不现实）

        # 卖出信号：有持仓 且 MACD死叉（MACD线从上方穿越信号线）
        elif signal == -1:
            self.log(f'MACD死叉卖出信号, 当前收盘价: {self.data.close[0]:.2f}')
            self.log(f'  -> 订单将在下一交易日执行')
            # 执行市价卖出，卖出全部持仓
            self.order = self.sell(exectype=bt.Order.Close)  # 使用当日收盘价执行（不现实）

        # # 买入信号：没有持仓 且 MACD金叉（MACD线从下方穿越信号线）
        # if not self.position and self.crossover > 0:
        #     self.log(f'MACD金叉买入信号, 当前收盘价: {self.data.close[0]:.2f}')
        #     self.log(f'  -> 订单将在下一交易日执行')
        #     # 执行市价买入，买入全部可用资金
        #     self.order = self.buy(exectype=bt.Order.Close)  # 使用当日收盘价执行（不现实）

        # # 卖出信号：有持仓 且 MACD死叉（MACD线从上方穿越信号线）
        # elif self.position and self.crossover < 0:
        #     self.log(f'MACD死叉卖出信号, 当前收盘价: {self.data.close[0]:.2f}')
        #     self.log(f'  -> 订单将在下一交易日执行')
        #     # 执行市价卖出，卖出全部持仓
        #     self.order = self.sell(exectype=bt.Order.Close)  # 使用当日收盘价执行（不现实）

    def log(self, txt, dt=None):
        """
        日志输出方法

        用于输出策略运行过程中的重要信息，如交易信号、订单执行等。

        Args:
            txt (str): 要输出的日志内容
            dt (datetime, optional): 日志时间，默认使用当前数据点的时间
        """
        if self.params.printlog:
            # 获取当前数据点的日期，如果没有指定dt则使用当前日期
            dt = dt or self.datas[0].datetime.date(0)
            print(f'{dt.isoformat()}, {txt}')


class BacktestSystem:
    """
    回测系统主类（带缓存功能）
    ========================

    这是整个回测系统的核心类，整合了数据获取、策略回测、结果分析和可视化等功能。

    主要功能：
    1. 数据管理：通过LongBridgeData获取历史数据（支持缓存）
    2. 策略回测：使用Backtrader框架执行策略回测
    3. 结果分析：计算各种绩效指标和风险指标
    4. 可视化：生成交互式图表展示回测结果
    5. **新增：缓存管理功能**

    工作流程：
    1. 下载指定股票的历史数据（优先使用缓存）
    2. 配置Backtrader回测环境
    3. 运行策略回测
    4. 分析回测结果
    5. 生成可视化图表

    支持的分析指标：
    - 总收益率、年化收益率
    - 夏普比率、最大回撤
    - 交易次数、胜率
    - 平均盈利、平均亏损

    缓存功能：
    - 自动缓存下载的历史数据
    - 支持缓存信息查看和管理
    - 可选择强制重新下载数据
    """

    def __init__(self, plotter=None, enable_cache=True, cache_dir="data_cache", disguise_mode=False):
        """
        初始化回测系统

        Args:
            plotter (BacktestPlotter, optional): 自定义绘图器，如果不提供则使用默认绘图器
            enable_cache (bool): 是否启用缓存功能，默认True
            cache_dir (str): 缓存目录路径，默认"data_cache"
            disguise_mode (bool): 是否启用伪装模式，将金融术语替换为频率术语
        """
        # 创建LongBridge数据下载器实例（带缓存功能）
        self.data_downloader = LongBridgeData(enable_cache=enable_cache, cache_dir=cache_dir)
        # 存储不同股票的回测结果，key为股票代码，value为回测结果字典
        self.results = {}
        # 创建或使用提供的绘图器
        self.plotter = plotter if plotter is not None else BacktestPlotter(disguise_mode=disguise_mode)
        # 缓存配置
        self.enable_cache = enable_cache
        # 伪装模式配置
        self.disguise_mode = disguise_mode

    def run_backtest(self, symbol, start_date, end_date, initial_cash=100000, period=Period.Day, force_download=False):
        """
        运行完整的回测流程（支持缓存和多种时间周期）
        ============================================

        这是回测系统的核心方法，执行完整的回测流程并返回详细结果。

        Args:
            symbol (str): 股票代码，格式如'AAPL.US', '00700.HK'
            start_date (datetime): 回测开始日期
            end_date (datetime): 回测结束日期
            initial_cash (float): 初始资金，默认10万
            period (Period): 时间周期，支持：
                           - Period.Day: 日线（默认）
                           - Period.Min_1: 1分钟线
                           - Period.Min_5: 5分钟线
                           - Period.Min_15: 15分钟线
                           - Period.Min_30: 30分钟线
                           - Period.Min_60: 60分钟线
                           - 其他LongPort支持的周期
            force_download (bool): 是否强制重新下载数据，忽略缓存，默认False

        Returns:
            dict: 包含完整回测结果的字典，包括：
                - 基本信息：股票代码、时间范围、资金情况
                - 收益指标：总收益率、夏普比率等
                - 风险指标：最大回撤等
                - 交易统计：交易次数、胜率、平均盈亏等
                - 原始数据：价格数据、策略实例等

        Returns None: 如果数据下载失败或回测出错
        """
        # 获取周期字符串用于显示
        period_str = str(period).split('.')[-1]

        # 打印回测开始信息
        print(f"\n{'='*50}")
        print(f"开始回测 {symbol}")
        print(f"时间周期: {period_str}")
        print(f"时间范围: {start_date.date()} 到 {end_date.date()}")
        print(f"初始资金: ${initial_cash:,.2f}")
        if self.enable_cache:
            cache_status = "强制重新下载" if force_download else "优先使用缓存"
            print(f"缓存策略: {cache_status}")
        else:
            print(f"缓存状态: 已禁用")
        print(f"{'='*50}")

        # 第一步：下载历史数据（支持缓存和多种时间周期）
        df = self.data_downloader.download_data(symbol, start_date, end_date, period=period, force_download=force_download)
        if df is None or len(df) == 0:
            print("数据获取失败，无法进行回测")
            return None
        
        # 第二步：创建Backtrader回测引擎
        # Cerebro是Backtrader的核心引擎，负责协调所有回测组件
        cerebro = bt.Cerebro(runonce=False)

        # 第三步：添加数据源
        # 将pandas DataFrame转换为Backtrader可识别的数据格式
        data = bt.feeds.PandasData(dataname=df)
        cerebro.adddata(data)
        cerebro.origin_df = df

        # 第四步：添加交易策略
        # 将我们定义的MACD策略添加到回测引擎
        cerebro.addstrategy(MACDStrategy, origin_df = df)

        # 第五步：设置初始资金
        # 设置回测开始时的账户资金
        cerebro.broker.setcash(initial_cash)

        # 第六步：设置交易成本
        # 设置手续费为0.1%，模拟真实交易成本
        cerebro.broker.setcommission(commission=0.001)

        # 第七步：添加性能分析器
        # 这些分析器会自动计算各种回测指标
        cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name="trades")    # 交易分析
        cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name="sharpe")      # 夏普比率
        cerebro.addanalyzer(bt.analyzers.DrawDown, _name="drawdown")       # 回撤分析
        cerebro.addanalyzer(bt.analyzers.Returns, _name="returns")         # 收益分析
        
        # 第八步：执行回测
        print('\n开始运行回测...')
        # 记录回测开始时的账户价值
        start_value = cerebro.broker.getvalue()
        # 运行回测，返回策略实例列表
        results = cerebro.run()
        # 记录回测结束时的账户价值
        end_value = cerebro.broker.getvalue()

        # 第九步：提取回测结果
        # 获取策略实例（results是列表，我们只有一个策略0）
        strategy = results[0]

        # 计算基本收益统计
        # 总收益率 = (期末价值 - 期初价值) / 期初价值 * 100%
        total_return = ((end_value - start_value) / start_value) * 100

        # 从各个分析器中提取详细统计数据
        trade_analyzer = strategy.analyzers.trades.get_analysis()      # 交易统计
        sharpe_ratio = strategy.analyzers.sharpe.get_analysis().get('sharperatio', 0)  # 夏普比率
        drawdown = strategy.analyzers.drawdown.get_analysis()          # 回撤统计
        returns_analyzer = strategy.analyzers.returns.get_analysis()   # 收益统计（暂未使用）
        
        # 第十步：整理回测结果
        # 将所有回测数据和统计指标整理成字典格式，便于后续分析和展示
        results_dict = {
            # 基本信息
            'symbol': symbol,                    # 股票代码
            'start_date': start_date,           # 回测开始日期
            'end_date': end_date,               # 回测结束日期
            'initial_cash': initial_cash,       # 初始资金

            # 资金变化
            'start_value': start_value,         # 期初账户价值
            'end_value': end_value,             # 期末账户价值
            'total_return': total_return,       # 总收益率(%)

            # 风险收益指标
            'sharpe_ratio': sharpe_ratio,       # 夏普比率（风险调整后收益）
            'max_drawdown': drawdown.get('max', {}).get('drawdown', 0),  # 最大回撤(%)

            # 交易统计
            'trade_count': trade_analyzer.get('total', {}).get('total', 0),      # 总交易次数
            'win_count': trade_analyzer.get('won', {}).get('total', 0),          # 盈利交易次数
            'lose_count': trade_analyzer.get('lost', {}).get('total', 0),        # 亏损交易次数
            'win_rate': 0,                      # 胜率(%)，稍后计算
            'avg_win': trade_analyzer.get('won', {}).get('pnl', {}).get('average', 0),   # 平均盈利
            'avg_lose': trade_analyzer.get('lost', {}).get('pnl', {}).get('average', 0), # 平均亏损

            # 原始数据（用于绘图和进一步分析）
            'data': cerebro.origin_df,                         # 价格数据DataFrame
            'strategy': strategy                # 策略实例（包含交易记录等）
        }

        # 计算胜率：盈利交易次数 / 总交易次数 * 100%
        if results_dict['trade_count'] > 0:
            results_dict['win_rate'] = (results_dict['win_count'] / results_dict['trade_count']) * 100

        # 将结果存储到实例变量中，便于后续访问
        self.results[symbol] = results_dict

        # 打印回测结果摘要
        self.print_results(results_dict)

        return results_dict
    
    def print_results(self, results):
        """
        打印回测结果摘要
        ================

        以格式化的方式在控制台输出回测的关键指标和统计信息。

        Args:
            results (dict): 包含回测结果的字典
        """
        if self.disguise_mode:
            print(f"\n{'='*30} 频响分析结果 {'='*30}")

            # 基本信息
            print(f"信号代码: {results['symbol']}")
            print(f"初始功率: {results['initial_cash']:,.2f}Hz")
            print(f"最终功率: {results['end_value']:,.2f}Hz")

            # 收益指标
            print(f"总增益: {results['total_return']:.2f}dB")

            # 夏普比率可能为None，需要特殊处理
            sharpe_ratio = results['sharpe_ratio']
            if sharpe_ratio is not None:
                print(f"信噪比: {sharpe_ratio:.4f}")
            else:
                print(f"信噪比: N/A")  # 数据不足时无法计算

            # 风险指标
            print(f"最大衰减: {results['max_drawdown']:.2f}dB")

            # 交易统计
            print(f"调制次数: {results['trade_count']}")
            print(f"增强次数: {results['win_count']}")
            print(f"衰减次数: {results['lose_count']}")
            print(f"有效率: {results['win_rate']:.2f}%")

            # 平均盈亏（只在有相应交易时显示）
            if results['avg_win'] != 0:
                print(f"平均增益: {results['avg_win']:.2f}Hz")
            if results['avg_lose'] != 0:
                print(f"平均损耗: {results['avg_lose']:.2f}Hz")
        else:
            print(f"\n{'='*30} 回测结果 {'='*30}")

            # 基本信息
            print(f"股票代码: {results['symbol']}")
            print(f"初始资金: ${results['initial_cash']:,.2f}")
            print(f"最终资金: ${results['end_value']:,.2f}")

            # 收益指标
            print(f"总收益率: {results['total_return']:.2f}%")

            # 夏普比率可能为None，需要特殊处理
            sharpe_ratio = results['sharpe_ratio']
            if sharpe_ratio is not None:
                print(f"夏普比率: {sharpe_ratio:.4f}")
            else:
                print(f"夏普比率: N/A")  # 数据不足时无法计算

            # 风险指标
            print(f"最大回撤: {results['max_drawdown']:.2f}%")

            # 交易统计
            print(f"交易次数: {results['trade_count']}")
            print(f"胜利次数: {results['win_count']}")
            print(f"失败次数: {results['lose_count']}")
            print(f"胜率: {results['win_rate']:.2f}%")

            # 平均盈亏（只在有相应交易时显示）
            if results['avg_win'] != 0:
                print(f"平均盈利: ${results['avg_win']:.2f}")
            if results['avg_lose'] != 0:
                print(f"平均亏损: ${results['avg_lose']:.2f}")

        print(f"{'='*70}")
    
    def plot_results(self, symbol, custom_plotter=None):
        """
        使用绘图器绘制回测结果
        =====================

        使用BacktestPlotter类绘制回测结果图表。
        支持自定义绘图器，提供更大的灵活性。

        Args:
            symbol (str): 要绘制的股票代码
            custom_plotter (BacktestPlotter, optional): 自定义绘图器

        Returns:
            plotly.graph_objects.Figure: Plotly图表对象，可以显示或保存
        """
        # 检查是否存在该股票的回测结果
        if symbol not in self.results:
            print(f"未找到 {symbol} 的回测结果")
            return None

        # 选择使用的绘图器
        plotter = custom_plotter if custom_plotter is not None else self.plotter

        # 获取回测结果
        results_dict = self.results[symbol]

        # 使用绘图器生成图表
        fig = plotter.plot_macd_strategy_results(results_dict)

        return fig

    def get_cache_info(self):
        """
        获取缓存信息

        Returns:
            dict: 缓存统计信息，如果缓存未启用则返回None
        """
        return self.data_downloader.get_cache_info()

    def clear_cache(self, symbol=None):
        """
        清理缓存

        Args:
            symbol (str, optional): 如果指定，只清理该股票的缓存；否则清理全部
        """
        self.data_downloader.clear_cache(symbol)

    def print_cache_info(self):
        """打印缓存信息"""
        self.data_downloader.print_cache_info()


def main():
    """
    主函数 - 回测系统使用示例（带缓存功能和伪装模式演示）
    ====================================================

    演示如何使用回测系统进行完整的策略回测流程，包括缓存功能和伪装模式的使用。
    包括参数设置、回测执行、结果展示、缓存管理等步骤。

    这个示例展示了对苹果股票(AAPL.US)在2023年的MACD策略回测。
    """
    # 伪装模式配置 - 设置为True启用伪装模式
    DISGUISE_MODE = True  # 改为False可关闭伪装模式

    # 第一步：创建回测系统实例（启用缓存和伪装模式）
    backtest_system = BacktestSystem(
        enable_cache=True,           # 启用缓存功能
        cache_dir="data_cache",      # 设置缓存目录
        disguise_mode=DISGUISE_MODE  # 启用伪装模式
    )

    # 第二步：设置回测参数
    symbol = "YINN.US"                    # 股票代码：苹果公司
    start_date = datetime(2025, 1, 1)     # 回测开始日期：2023年1月1日
    end_date = datetime(2025, 7, 24)       # 回测结束日期：2024年1月1日
    initial_cash = 100000                 # 初始资金：10万美元

    # 第三步：显示缓存信息（回测前）
    print("\n=== 回测前缓存状态 ===")
    backtest_system.print_cache_info()

    # 第四步：执行回测（首次运行会下载并缓存数据）
    print("\n=== 执行回测（首次运行） ===")
    results = backtest_system.run_backtest(
        symbol=symbol,
        start_date=start_date,
        end_date=end_date,
        initial_cash=initial_cash,
        period=Period.Min_60,
        force_download=False  # 优先使用缓存
    )

    # 第五步：显示缓存信息（回测后）
    print("\n=== 回测后缓存状态 ===")
    backtest_system.print_cache_info()

    # # 第六步：演示缓存功能 - 再次运行相同回测（应该使用缓存）
    # print("\n=== 演示缓存功能 - 再次运行相同回测 ===")
    # results_cached = backtest_system.run_backtest(
    #     symbol=symbol,
    #     start_date=start_date,
    #     end_date=end_date,
    #     initial_cash=initial_cash,
    #     force_download=False  # 应该使用缓存数据
    # )

    # 第七步：处理回测结果
    if results:
        # 生成可视化图表
        fig = backtest_system.plot_results(symbol)
        if fig:
            # 显示交互式图表
            fig.show()

            # 可选：保存图表为HTML文件
            # fig.write_html(f"{symbol}_backtest_results.html")
            print(f"\n回测完成! 图表已显示。")
        else:
            print("图表生成失败")
    else:
        print("回测失败")

    # 第八步：演示缓存管理功能
    print(f"\n=== 缓存管理演示 ===")
    print("可用的缓存管理命令:")
    print("1. backtest_system.print_cache_info()  # 查看缓存信息")
    print("2. backtest_system.clear_cache()       # 清理全部缓存")
    print(f"3. backtest_system.clear_cache('{symbol}')  # 清理特定股票缓存")
    print("4. force_download=True                 # 强制重新下载数据")


def demo_cache_management():
    """
    缓存管理功能演示
    ===============

    演示如何使用各种缓存管理功能
    """
    print("\n" + "="*60)
    print("缓存管理功能演示")
    print("="*60)

    # 创建回测系统
    system = BacktestSystem(enable_cache=True)

    # 演示参数
    symbol = "AAPL.US"
    start_date = datetime(2023, 6, 1)
    end_date = datetime(2023, 12, 31)

    print("\n1. 首次下载数据（会自动缓存）")
    system.run_backtest(symbol, start_date, end_date, initial_cash=50000)

    print("\n2. 查看缓存信息")
    system.print_cache_info()

    print("\n3. 再次运行（使用缓存）")
    system.run_backtest(symbol, start_date, end_date, initial_cash=50000)

    print("\n4. 强制重新下载")
    system.run_backtest(symbol, start_date, end_date, initial_cash=50000, force_download=True)

    print("\n5. 清理特定股票缓存")
    system.clear_cache(symbol)

    print("\n6. 查看清理后的缓存信息")
    system.print_cache_info()


def demo_disguise_mode():
    """
    伪装模式功能演示
    ===============

    演示伪装模式的效果，对比正常模式和伪装模式的显示差异
    """
    print("\n" + "="*60)
    print("伪装模式功能演示")
    print("="*60)

    # 演示参数
    symbol = "AAPL.US"
    start_date = datetime(2023, 1, 1)
    end_date = datetime(2023, 3, 1)
    initial_cash = 50000

    print(f"\n1. 正常模式回测:")
    print("-" * 40)
    # 创建正常模式系统
    normal_system = BacktestSystem(enable_cache=True, disguise_mode=False)
    normal_results = normal_system.run_backtest(symbol, start_date, end_date, initial_cash)

    if normal_results:
        print(f"\n正常模式图表:")
        normal_fig = normal_system.plot_results(symbol)
        if normal_fig:
            normal_fig.show()

    print(f"\n" + "="*60)
    print(f"\n2. 伪装模式回测:")
    print("-" * 40)
    # 创建伪装模式系统
    disguise_system = BacktestSystem(enable_cache=True, disguise_mode=True)
    disguise_results = disguise_system.run_backtest(symbol, start_date, end_date, initial_cash)

    if disguise_results:
        print(f"\n伪装模式图表:")
        disguise_fig = disguise_system.plot_results(symbol)
        if disguise_fig:
            disguise_fig.show()

    print(f"\n" + "="*60)
    print("演示完成！")
    print("注意观察两种模式下术语的差异：")
    print("- 正常模式：价格、MACD、买入/卖出信号、收益率等")
    print("- 伪装模式：频率、频差、增强/衰减响应、增益等")
    print("="*60)


if __name__ == "__main__":
    """
    程序入口点
    ==========

    当直接运行此脚本时执行的代码。
    首先显示系统介绍信息，然后运行回测示例。
    """
    # 显示系统介绍和功能特点
    print("LongBridge + Backtrader MACD回测系统 (带缓存功能和伪装模式)")
    print("="*70)
    print("功能特点:")
    print("1. 使用LongBridge API获取实时历史数据")
    print("2. 基于MACD指标的量化交易策略")
    print("3. 完整的回测框架和风险分析")
    print("4. 使用Plotly进行专业级可视化")
    print("5. 详细的交易统计和绩效指标")
    print("6. ✨ 智能数据缓存功能")
    print("   - 自动缓存下载的历史数据")
    print("   - 优先使用本地离线数据")
    print("   - 减少API调用，提高回测速度")
    print("   - 支持缓存管理和清理")
    print("7. 🎭 新增：伪装模式功能")
    print("   - 将金融术语替换为频率/信号处理术语")
    print("   - 价格→频率，MACD→频差，买入/卖出→增强/衰减")
    print("   - 收益率→增益，夏普比率→信噪比")
    print("   - 有效隐藏量化交易的真实目的")
    print("="*70)

    # 运行主程序示例
    main()

    # 可选：运行其他演示
    # 取消下面的注释来运行相应演示
    # demo_cache_management()    # 缓存管理演示
    # demo_disguise_mode()       # 伪装模式演示
